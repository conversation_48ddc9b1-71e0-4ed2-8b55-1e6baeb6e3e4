#!/usr/bin/env python3

import sys

def read_ppm_pixel(filename, x, y):
    """Read a specific pixel from a PPM file"""
    with open(filename, 'rb') as f:
        # Read header
        magic = f.readline().decode().strip()
        if magic != 'P6':
            raise ValueError(f"Not a P6 PPM file: {magic}")
        
        # Skip comments
        line = f.readline().decode().strip()
        while line.startswith('#'):
            line = f.readline().decode().strip()
        
        # Parse dimensions
        width, height = map(int, line.split())
        
        # Parse max value
        max_val = int(f.readline().decode().strip())
        
        # Calculate pixel offset
        pixel_offset = (y * width + x) * 3
        
        # Seek to pixel and read RGB values
        f.seek(f.tell() + pixel_offset)
        rgb = f.read(3)
        
        if len(rgb) != 3:
            raise ValueError("Could not read pixel data")
        
        # Convert to float values (0-1 range)
        r = rgb[0] / max_val
        g = rgb[1] / max_val
        b = rgb[2] / max_val
        
        return (r, g, b)

if __name__ == "__main__":
    if len(sys.argv) != 4:
        print("Usage: python3 check_pixel.py <ppm_file> <x> <y>")
        sys.exit(1)
    
    filename = sys.argv[1]
    x = int(sys.argv[2])
    y = int(sys.argv[3])
    
    try:
        r, g, b = read_ppm_pixel(filename, x, y)
        print(f"Pixel [{y}][{x}] in {filename}: R={r:.6f}, G={g:.6f}, B={b:.6f}")
    except Exception as e:
        print(f"Error: {e}")
